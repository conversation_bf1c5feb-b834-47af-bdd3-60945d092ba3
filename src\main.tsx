import { createRoot, type Root } from 'react-dom/client';
import ChatWidget from './ChatWidget.tsx';
import { DEFAULT_API_KEY, DEFAULT_API_URL } from './constants.ts'; // Use constants

export interface WidgetConfig {
  apiKey?: string;
  apiUrl?: string;
  websiteUrl?: string;
  customer_name?: string;
}

declare global {
  interface Window {
    initChatWidget?: (config?: WidgetConfig) => void;
    QUOTE_AI_API_URL?: string; // Global API URL override
    QUOTE_AI_CONFIG?: WidgetConfig; // Store widget configuration globally
  }
}

// Keep track of the React root to prevent multiple createRoot calls
let widgetRoot: Root | null = null;

const renderWidget = (config: WidgetConfig) => {
  let container = document.getElementById('quoteai-widget-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'quoteai-widget-container';
    document.body.appendChild(container);
  }

  // Store config globally for API access
  window.QUOTE_AI_CONFIG = config;

  // Create root only once, reuse for subsequent renders
  if (!widgetRoot) {
    widgetRoot = createRoot(container!);
  }

  widgetRoot.render(
    <ChatWidget apiKey={config.apiKey || DEFAULT_API_KEY} apiUrl={config.apiUrl || DEFAULT_API_URL} />
  );
};

// Auto-initialize the widget
const autoInitializeWidget = () => {
  console.info('QuoteAI Widget: Auto-initializing...');
  const config: WidgetConfig = {
    apiUrl: window.QUOTE_AI_API_URL || DEFAULT_API_URL,
    apiKey: DEFAULT_API_KEY,
  };

  renderWidget(config);
};

// Provide manual initialization function
window.initChatWidget = function(config = {}) {
  console.info('QuoteAI Widget: Manual initialization with config:', config);

  const fullConfig: WidgetConfig = {
    apiKey: config.apiKey || DEFAULT_API_KEY,
    apiUrl: config.apiUrl || window.QUOTE_AI_API_URL || DEFAULT_API_URL,
    websiteUrl: config.websiteUrl,
    customer_name: config.customer_name,
  };

  if (config.apiUrl) {
    window.QUOTE_AI_API_URL = config.apiUrl; // Allow global override via init
  }

  renderWidget(fullConfig);
};

// Auto-initialize if not manually done by the time DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (!document.getElementById('quoteai-widget-container')) {
    autoInitializeWidget();
  }
});

export default ChatWidget;